package cc.unitmesh.diagram.graphviz.model

/**
 * Represents a property/field of a Graphviz node
 * Similar to JdlEntityNodeField in JHipster UML implementation
 */
data class GraphvizNodeProperty(
    val name: String,
    val type: String? = null,
    val required: Boolean = false,
    val attributes: Map<String, String> = emptyMap()
) {
    
    /**
     * Get a specific attribute value
     */
    fun getAttribute(key: String): String? = attributes[key]
    
    /**
     * Check if this property has a specific attribute
     */
    fun hasAttribute(key: String): Boolean = attributes.containsKey(key)
    
    /**
     * Get the display name for this property
     */
    fun getDisplayName(): String = name
    
    /**
     * Get the display type for this property
     */
    fun getDisplayType(): String? = type
    
    /**
     * Check if this property is required
     */
    fun isRequired(): Boolean = required
    
    override fun toString(): String {
        val typeStr = if (type != null) ": $type" else ""
        val requiredStr = if (required) " (required)" else ""
        return "$name$typeStr$requiredStr"
    }
}

/**
 * Types of Graphviz node properties
 */
enum class GraphvizPropertyType {
    FIELD,      // Regular field/property
    METHOD,     // Method/function
    ATTRIBUTE   // Generic attribute
}
