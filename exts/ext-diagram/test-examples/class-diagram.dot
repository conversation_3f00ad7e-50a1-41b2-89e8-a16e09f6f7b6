digraph ClassDiagram {
    rankdir=TB;
    
    // Regular nodes
    node1 [label="Simple Node"];
    node2 [label="Another Node", shape=box];
    
    // Record-shaped nodes with fields (like classes)
    User [shape=record, label="{User|id:Long|name:String|email:String|active:Boolean}"];
    Order [shape=record, label="{Order|id:Long|userId:Long|amount:Double|status:String}"];
    Product [shape=record, label="{Product|id:Long|name:String|price:Double|category:String}"];
    
    // Method-like records
    UserService [shape=record, label="{UserService|+createUser(name:String):User|+findById(id:Long):User|+updateUser(user:User):User}"];
    
    // Edges
    node1 -> node2;
    User -> Order [label="has many"];
    Order -> Product [label="contains"];
    UserService -> User [label="manages"];
}
